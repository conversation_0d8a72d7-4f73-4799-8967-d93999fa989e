import { expect } from "chai";
import * as sinon from "sinon";
import { BaseGameUrlStrategy } from "../../../skywind/services/gameUrl/baseGameUrlStrategy";
import { EntityStaticDomainPoolService } from "../../../skywind/services/entityStaticDomainPool";
import { StaticDomainType } from "../../../skywind/entities/domain";
import config from "../../../skywind/config";

// Create a concrete implementation for testing
class TestGameUrlStrategy extends BaseGameUrlStrategy {
    constructor(entityGame: any, brand: any, entitySettings: any, isLobby: boolean) {
        super(entityGame, brand, entitySettings, isLobby);
    }

    protected validateBonusCoinsAvailable(): Promise<void> {
        return Promise.resolve();
    }

    protected getTokenData(): Promise<any> {
        return Promise.resolve({});
    }

    protected getCurrency(): string {
        return "USD";
    }

    protected getLanguage(): string {
        return "en";
    }

    protected getCountrySource(): Promise<any> {
        return Promise.resolve({});
    }

    public async testGetLobbyDomain() {
        // Access the private method for testing
        return (this as any).getLobbyDomain();
    }
}

describe("BaseGameUrlStrategy", () => {
    let sandbox: sinon.SinonSandbox;
    let strategy: TestGameUrlStrategy;
    let mockEntityGame: any;
    let mockBrand: any;
    let mockEntitySettings: any;
    let pickStaticDomainStub: sinon.SinonStub;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        
        mockEntityGame = {
            isLiveGame: () => false
        };
        
        mockBrand = {
            defaultLobbyDomain: null
        };
        
        mockEntitySettings = {
            launchGameInsideLobby: false
        };

        strategy = new TestGameUrlStrategy(mockEntityGame, mockBrand, mockEntitySettings, false);
        
        // Stub the EntityStaticDomainPoolService.pickStaticDomain method
        pickStaticDomainStub = sandbox.stub(EntityStaticDomainPoolService.prototype, "pickStaticDomain");
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe("getLobbyDomain", () => {
        it("should use static domain when available", async () => {
            const mockStaticDomain = { domain: "example.com" };
            pickStaticDomainStub.resolves(mockStaticDomain);

            const result = await strategy.testGetLobbyDomain();

            expect(pickStaticDomainStub.calledWith(StaticDomainType.LOBBY)).to.be.true;
            expect(result).to.equal("-example.com/#/direct-launch");
        });

        it("should use defaultLobbyDomain when static domain is not available", async () => {
            pickStaticDomainStub.resolves(undefined);
            mockBrand.defaultLobbyDomain = "-fallback.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal("-fallback.com/#/direct-launch");
        });

        it("should use config.lobbies.domainTemplate when both static domain and defaultLobbyDomain are not available", async () => {
            pickStaticDomainStub.resolves(undefined);
            mockBrand.defaultLobbyDomain = null;
            const originalDomainTemplate = config.lobbies.domainTemplate;
            config.lobbies.domainTemplate = "config-template.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal("-config-template.com/#/direct-launch");
            
            // Restore original config
            config.lobbies.domainTemplate = originalDomainTemplate;
        });

        it("should handle domain starting with dot correctly", async () => {
            pickStaticDomainStub.resolves(undefined);
            mockBrand.defaultLobbyDomain = ".example.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal(".example.com/play");
        });

        it("should add dash prefix when domain doesn't start with dash or dot", async () => {
            pickStaticDomainStub.resolves(undefined);
            mockBrand.defaultLobbyDomain = "example.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal("-example.com/#/direct-launch");
        });

        it("should not add extra dash when domain already starts with dash", async () => {
            pickStaticDomainStub.resolves(undefined);
            mockBrand.defaultLobbyDomain = "-example.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal("-example.com/#/direct-launch");
        });

        it("should prioritize static domain over defaultLobbyDomain", async () => {
            const mockStaticDomain = { domain: "static.com" };
            pickStaticDomainStub.resolves(mockStaticDomain);
            mockBrand.defaultLobbyDomain = "-fallback.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal("-static.com/#/direct-launch");
        });

        it("should prioritize defaultLobbyDomain over config template", async () => {
            pickStaticDomainStub.resolves(undefined);
            mockBrand.defaultLobbyDomain = "-fallback.com";
            const originalDomainTemplate = config.lobbies.domainTemplate;
            config.lobbies.domainTemplate = "config-template.com";

            const result = await strategy.testGetLobbyDomain();

            expect(result).to.equal("-fallback.com/#/direct-launch");
            
            // Restore original config
            config.lobbies.domainTemplate = originalDomainTemplate;
        });
    });
});
