import { MerchantInfo } from "./merchant";
import { Transaction } from "sequelize";
import { EntityWallet } from "@skywind-group/sw-management-wallet";
import { LabelImpl } from "../services/label";
import { ShortJurisdiction } from "./jurisdiction";
import { DeploymentGroupAttributes } from "./deploymentGroup";

export interface FindEntityOptions {
    id?: number;
    name?: string;
    parent?: string;
    key?: string;
    path?: string;
}

export interface EntityBalance {
    main: number;
}

export interface EntityBalances {
    [currency: string]: EntityBalance;
}

export interface AdditionalFields {
    dynamicDomainId: number;
    staticDomainId: number;
    defaultCountry: string;
    defaultCurrency: string;
    defaultLanguage: string;
    environment: string;
}

export interface StructureInfoOptions {
    basePath?: string;
    decryptId?: boolean;
    additionalFields?: (keyof AdditionalFields)[];
}

export interface BaseEntity {
    id?: number;
    type: string;
    name: string;
    description?: string;
    environment?: string;

    status: string;

    key: string;
    path: string;
    title?: string;
    dynamicDomainId?: number;
    prevDynamicDomainId?: number;
    migrationStatus?: MIGRATION_STATUS;
    staticDomainId?: number;
    staticDomainTags?: string[];
    domains?: string[];
    labels?: LabelImpl[];

    staticDomainPoolId?: number;
    dynamicDomainPoolId?: number;
    defaultLobbyDomain?: string;

    checkWebSiteWhitelisted?: string;
    isTest?: boolean;

    readonly inheritedDynamicDomainId?: number;
    readonly inheritedDynamicDomainPoolId?: number;
    readonly inheritedEnvironment?: string;
    readonly inheritedWebSiteWhitelistedCheck?: string;
    readonly inheritedTestMode?: boolean;
    deploymentGroupId?: number;

    readonly wallet: EntityWallet;

    toInfo(decryptId?: boolean): Promise<EntityInfo>;

    toInfoWithBalances(decryptId?: boolean): Promise<EntityInfo & WithBalances>;

    structureToInfo(options?: StructureInfoOptions): Promise<EntityInfo>;

    structureToShortInfo(options?: StructureInfoOptions): EntityShortInfo;

    isMaster(): boolean;

    isBrand(): boolean;

    isSuspended(): boolean;

    underMaintenance(): boolean;

    find(options: FindEntityOptions): BaseEntity;

    save(transaction?: Transaction): Promise<this>;

    getVersion(): number;

    currencyExists(code: string): boolean;

    countryExists(code: string): boolean;

    languageExists(code: string): boolean;

    getCurrencies(): Array<string>;

    getCountries(): Array<string>;

    getLanguages(): Array<string>;

    addCurrency(code: string): void;

    addCountry(code: string): void;

    addLanguage(code: string): void;

    updateCurrencyArray(array: Array<string>): void;

    updateLanguageArray(array: Array<string>): void;

    updateMerchantTypesArray(array: Array<string>): Promise<void>;

    removeCurrency(code: string): void;

    removeCountry(code: string): void;

    removeLanguage(code: string): void;

    fetchBalance(currency: string): Promise<EntityBalance>;

    fetchBalances(): Promise<EntityBalances>;

    getInheritedDomains(): string[];

    domainExists(domain: string): boolean;

    getMerchantTypes(): Promise<string[]>;

    getChildInheritedMerchantTypes(): string[];

    merchantTypeExists(type: string, own?: boolean): Promise<boolean>;

    addMerchantType(type: string | string[]): Promise<void>;

    removeMerchantType(type: string | string[]): Promise<void>;

    validateStaticDomain(domain: string): boolean;

    getDomains(): string[];
}

export interface EntityWithChild<T extends BaseEntity = BaseEntity> extends BaseEntity {
    child: Array<T>;
}

export interface MasterEntity extends BaseEntity, EntityWithChild<Entity> {
}

export interface ChildEntity extends BaseEntity {
    parent: number;
    defaultCurrency: string;
    defaultCountry: string;
    defaultLanguage: string;

    getParent(): Entity;

    setParent(parent: Entity): void;

    isTopLevel(): boolean;

    setDefaultCurrency(code: string): void;

    setDefaultCountry(code: string): void;

    setDefaultLanguage(code: string): void;
}

export interface Entity extends ChildEntity, EntityWithChild<ChildEntity> {
    child: Array<ChildEntity>;
}

export interface WithBalances {
    balances: EntityBalances;
}

export interface WithMerchant {
    merchant: MerchantInfo;
    jurisdictionCode?: string;
}

export interface EntityInfo {
    id?: number | string;
    type: string;
    name: string;
    description?: string;

    status: string;

    key: string;

    defaultCurrency?: string;
    defaultCountry?: string;
    defaultLanguage?: string;

    countries?: Array<string>;
    currencies?: Array<string>;
    languages?: Array<string>;

    domains?: string[];
    parentDomains?: string[];

    child?: Array<EntityInfo>;
    path?: string;
    dynamicDomainId?: number;
    staticDomainId?: number;
    environment?: string;

    isTest?: boolean;
    isMerchant?: boolean;
    title?: string;
    merchantTypes?: string[];
    staticDomainTags?: string[];
    deploymentGroup?: DeploymentGroupAttributes;

    decryptedBrand?: number;
    merchantCode?: string;
    jurisdiction?: ShortJurisdiction[];
}

export interface MerchantEntityInfo extends EntityInfo {
    merchant: MerchantInfo;
}

export interface EntityWitPathInfo {
    readonly id: number;
    readonly fullPath: string;
}

export interface EntityShortInfo extends Partial<AdditionalFields> {
    id: number;
    child?: Array<EntityShortInfo>;
    path?: string;
    name?: string;
    title?: string;
    type?: string;
    decryptedBrand?: number;
    key: string;
    description?: string;
    isTest?: boolean;
    status?: string;

}

export interface WithDefaultBalance {
    defaultBalance: number;
}

export enum ENTITY_TYPE {
    ENTITY = "entity",
    BRAND = "brand",
    MERCHANT = "merchant",
}

export enum MIGRATION_STATUS {
    STARTED = "started",
    PROCESSING = "processing"
}

export interface EntityDomainInfo {
    key: string;
    dynamicDomainId?: number;
    staticDomainId?: number;
    environment?: string;
}

export interface WhiteList {
    parent?: string[];
    own?: string[];
}

export interface Includable<T, E = EntityInfo> {
    includeTo(entity: E): Promise<T & E>;
}

export enum WEBSITE_WHITELISTED_CHECK_LEVEL {
    NONE = "none",
    WARNING = "warning",
    ERROR = "error"
}

export interface EntityKeyPath {
    readonly key: string;
    readonly child?: EntityKeyPath;
}

export enum EntityStatus {
    NORMAL = "normal",
    SUSPENDED = "suspended",
    MAINTENANCE = "maintenance",
    BLOCKED_BY_ADMIN = "blocked_by_admin",
    TEST = "test"
}

export interface EntityAttributes {
    id?: number;
    name?: string;
    title?: string;
    type?: string;
    description?: string;
    path?: string;
    status?: string;
    key?: string;

    defaultCurrency?: string;
    defaultCountry?: string;
    defaultLanguage?: string;

    countries?: string[];
    currencies?: string[];
    languages?: string[];

    dynamicDomainId?: number;
    environment?: string;
    prevDynamicDomainId?: number;
    migrationStatus?: MIGRATION_STATUS;
    staticDomainId?: number;
    staticDomainTags?: string[];
    domains?: string[];

    staticDomainPoolId?: number;
    dynamicDomainPoolId?: number;
    defaultLobbyDomain?: string;

    isTest?: boolean;

    merchantTypes?: string[];
    deploymentGroupId?: number;

    createdAt?: Date;
    updatedAt?: Date;
    checkWebSiteWhitelisted?: string;

    parent?: any;
}
